<!-- Tag Assignment List Component -->
<div class="local-management-container">
  <!-- Header Section -->
  <div class="header-section">
    <div class="page-title">
      <h1 class="title">
        <mat-icon class="title-icon">assignment</mat-icon> Liste des Affectations
      </h1>
    </div>
  </div>

  <div class="loading-spinner" *ngIf="isLoading">Chargement...</div>

  <!-- Assignments Table -->
  <div class="table-section" *ngIf="!isLoading">
    <div class="table-header">
    
      <div class="table-stats">
        <span class="stats-badge">{{ assignments.length }} affectation(s)</span>
      </div>
    </div>

    <div class="table-container" *ngIf="assignments.length > 0; else noAssignments">
      <table class="data-table">
        <thead>
          <tr>
            <th>Tag</th>
            <th>Type de Cible</th>
            <th>Cible</th>
            <th>Date de Création</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let assignment of assignments">
            <td>
              <div class="tag-cell">
                <mat-icon class="tag-icon">local_offer</mat-icon>
                {{ getTagName(assignment) }}
              </div>
            </td>
            <td>
              <span class="target-type-badge" [class]="'type-' + getTargetTypeClass(assignment.TargetType)">
                {{ assignment.TargetType }}
              </span>
            </td>
            <td>{{ getTargetName(assignment) }}</td>
            <td>{{ assignment.CreatedAt | date:'dd/MM/yyyy HH:mm' }}</td>
            <td>
              <div class="action-buttons">
                <button class="btn-action btn-delete" (click)="deleteAssignment(assignment)" title="Supprimer">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <ng-template #noAssignments>
      <div class="empty-state">
        <mat-icon class="empty-icon">assignment</mat-icon>
        <h3>Aucune affectation trouvée</h3>
        <p>Aucune affectation de tag n'a été créée pour le moment</p>
      </div>
    </ng-template>
  </div>
</div>
