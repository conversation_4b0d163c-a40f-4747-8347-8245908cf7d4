import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { CommonModule } from '@angular/common';

import { TagApiService } from '@app/core/services/administrative/tag.service';
import { Tag } from '@app/core/models/tag';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { Page, Lister, SortPage, FilterParam, Pagination } from '@app/core/models/util/page';

@Component({
  selector: 'app-tag-list',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatPaginatorModule,
    MatDialogModule,
    MatSnackBarModule,
    GenericTableComponent
  ],
  templateUrl: './tag-list.component.html',
  styleUrls: ['./tag-list.component.css']
})
export class TagListComponent implements OnInit {
  // Form
  tagForm: FormGroup;

  // Data
  tags: Tag[] = [];
  filteredTags: Tag[] = [];
  
  // UI State
  isLoading = false;
  isSubmitting = false;
  showTagForm = false;
  searchTerm = '';

  // Pagination
  currentPage = 0;
  pageSize = 10;
  totalCount = 0;

  // Sorting
  currentSort: SortPage[] = [{
    Column: 'CreatedAt',
    Sort: 'desc'
  }];

  constructor(
    private fb: FormBuilder,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private tagService: TagApiService
  ) {
    this.tagForm = this.fb.group({
      id: [''],
      nom: ['', [Validators.required, Validators.minLength(2)]]
    });
  }

  ngOnInit(): void {
    this.loadTags();
  }

  loadTags(): void {
    this.isLoading = true;
    
    const request: Lister = {
      Pagination: {
        CurrentPage: this.currentPage + 1,
        PageSize: this.pageSize
      },
      SortParams: this.currentSort,
      FilterParams: this.buildFilterParams()
    };

    this.tagService.gatePage(request).subscribe({
      next: (response: Page<Tag>) => {
        this.filteredTags = response.Content ?? [];
        
        const pag = response?.Lister?.Pagination;
        if (pag && pag.totalElement !== undefined) {
          this.totalCount = pag.totalElement;
          this.pageSize = pag.PageSize ?? this.pageSize;
          this.currentPage = (pag.CurrentPage ?? 1) - 1;
        } else {
          this.totalCount = this.filteredTags.length;
        }
        
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading tags:', error);
        this.showError('Erreur lors du chargement des tags');
        this.isLoading = false;
      }
    });
  }

  filterTags(): void {
    this.currentPage = 0;
    this.loadTags();
  }

  onSearchKeyup(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.filterTags();
    } else if (event.key === 'Backspace' && this.searchTerm === '') {
      this.loadTags();
    }
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadTags();
  }

  private buildFilterParams(): FilterParam[] {
    const filters: FilterParam[] = [];
    const searchTerm = this.searchTerm.trim();

    if (searchTerm) {
      filters.push({
        Column: 'Nom',
        Value: searchTerm,
        Op: 'contains'
      });
    }

    return filters;
  }

  showCreateTagForm(): void {
    this.tagForm.reset();
    this.showTagForm = true;
  }

  hideTagForm(): void {
    this.showTagForm = false;
    this.tagForm.reset();
    this.isSubmitting = false;
  }

  editTag(tag: Tag): void {
    this.tagForm.patchValue(tag);
    this.showTagForm = true;
  }

  async onSubmitTag(): Promise<void> {
    if (this.tagForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    const formValue = this.tagForm.value;
    
    try {
      if (formValue.id) {
        // Update existing tag
        await this.tagService.update(formValue).toPromise();
        this.showSuccess('Tag mis à jour avec succès');
      } else {
        // Create new tag
        await this.tagService.create(formValue).toPromise();
        this.showSuccess('Tag créé avec succès');
      }
      
      this.hideTagForm();
      this.loadTags();
    } catch (error) {
      console.error('Error saving tag:', error);
      this.showError('Erreur lors de la sauvegarde du tag');
    } finally {
      this.isSubmitting = false;
    }
  }

  deleteTag(tag: Tag): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmer la suppression',
        message: 'Êtes-vous sûr de vouloir supprimer ce tag ?',
        icon: 'delete',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && tag.Id) {
        this.isLoading = true;
        this.tagService.delete(tag.Id).subscribe({
          next: () => {
            this.showSuccess('Tag supprimé avec succès');
            this.loadTags();
          },
          error: (error) => {
            console.error('Error deleting tag:', error);
            this.showError('Erreur lors de la suppression du tag');
            this.isLoading = false;
          }
        });
      }
    });
  }

  handleAction(event: { action: string; row: any }): void {
    const { action, row } = event;
    if (action === 'edit') {
      this.editTag(row);
    } else if (action === 'delete') {
      this.deleteTag(row);
    }
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
}