<!-- Tag Management Component -->
<div class="tag-management-container">
  <!-- Header Section -->
  <div class="header-section">
    <div class="page-title">
      <h1 class="title">
        <mat-icon class="title-icon">local_offer</mat-icon>
        Gestion des Tags
      </h1>
      <p class="subtitle">Créer et gérer les tags et leurs affectations</p>
    </div>
    <div class="actions">
      <button class="create-button" (click)="showCreateTagForm()">
        <mat-icon>add</mat-icon>
        Créer un Tag
      </button>
    </div>
  </div>

  <!-- Search Section -->
  <div class="search-bar">
    <input
      type="text"
      [(ngModel)]="searchTerm"
      placeholder="Rechercher un tag"
      (keyup)="onSearchKeyup($event)"
    />
    <button class="search-button" (click)="filterTags()">
      <mat-icon>search</mat-icon>
    </button>
  </div>

  <!-- Loading Spinner -->
  <div class="loading-spinner" *ngIf="isLoading">
    <mat-icon class="spinning">refresh</mat-icon>
    Chargement...
  </div>

  <!-- Tag Table -->
  <div class="table-section" *ngIf="!isLoading">
    <app-generic-table
      [headers]="['Nom', 'Date de Création', 'Actions']"
      [keys]="['Nom', 'CreatedAt', 'actions']"
      [data]="filteredTags"
      [actions]="['edit', 'delete']"
      (actionTriggered)="handleAction($event)"
    >
      <ng-template #cellNom let-tag>
        <div class="tag-cell">
          <mat-icon class="tag-icon">local_offer</mat-icon>
          {{ tag.Nom }}
        </div>
      </ng-template>
      <ng-template #cellCreatedAt let-tag>
        {{ tag.CreatedAt | date:'dd/MM/yyyy HH:mm' }}
      </ng-template>
    </app-generic-table>

    <mat-paginator
      [length]="totalCount"
      [pageSize]="pageSize"
      [pageIndex]="currentPage"
      [pageSizeOptions]="[5, 10, 25, 50]"
      (page)="onPageChange($event)"
      aria-label="Select page">
    </mat-paginator>
  </div>

  <!-- Create/Edit Tag Popup -->
  <div class="popup-overlay" *ngIf="showTagForm" (click)="hideTagForm()">
    <div class="popup-form" (click)="$event.stopPropagation()">
      <div class="popup-header">
        <h3>
          <mat-icon>{{ tagForm.get('id')?.value ? 'edit' : 'add_circle' }}</mat-icon>
          {{ tagForm.get('id')?.value ? 'Modifier le Tag' : 'Nouveau Tag' }}
        </h3>
        <button class="close-btn" (click)="hideTagForm()">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <form [formGroup]="tagForm" (ngSubmit)="onSubmitTag()" class="site-form">
        <div class="validation-errors" *ngIf="tagForm.invalid && (tagForm.touched || tagForm.dirty)">
          <div class="validation-errors-title">
            <mat-icon>error_outline</mat-icon>
            Erreurs de validation
          </div>
          <ul class="validation-errors-list">
            <li *ngIf="tagForm.get('nom')?.invalid">
              <mat-icon>error</mat-icon>
              Le nom du tag est requis (minimum 2 caractères)
            </li>
          </ul>
        </div>

        <div class="form-grid">
          <div class="form-group full-width">
            <label for="tagName">Nom du Tag <span class="required">*</span></label>
            <input 
              id="tagName" 
              type="text" 
              formControlName="nom" 
              placeholder="Entrez le nom du tag"
              required 
            />
          </div>
        </div>

        <div class="form-actions">
          <button type="button" class="btn-cancel" (click)="hideTagForm()">
            Annuler
          </button>
          <button type="submit" class="btn-submit" [disabled]="!tagForm.valid || isSubmitting">
            <mat-icon>{{ tagForm.get('id')?.value ? 'save' : 'add' }}</mat-icon>
            {{ isSubmitting ? 'Enregistrement...' : (tagForm.get('id')?.value ? 'Modifier' : 'Créer') }}
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading">
    <div class="loading-spinner">
      <mat-icon class="spinning">refresh</mat-icon>
      <p>Chargement...</p>
    </div>
  </div>
</div>