<!-- Tag Assignment Component -->
<div class="tag-assignment-container">
  <!-- Header Section -->
  <div class="header-card-container">
    <div class="header-section">
      <div class="page-title">
        <h1 class="title">
          <mat-icon class="title-icon">assignment</mat-icon>
          Affectation des Tags
        </h1>
        <p class="subtitle">Affecter des tags aux clients, sites et locaux</p>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="content-container">
    <mat-tab-group [(selectedIndex)]="selectedTab" class="custom-tabs">
      
      <!-- Tab 1: Create Assignment -->
      <mat-tab label="Nouvelle Affectation">
        <div class="tab-content">
          <div class="form-card">
            <div class="form-header">
              <h2>
                <mat-icon>assignment_add</mat-icon>
                Nouvelle Affectation
              </h2>
            </div>

            <!-- Validation Errors -->
            <div class="validation-errors" *ngIf="showValidationErrors && assignmentForm.invalid">
              <div class="validation-errors-title">
                <mat-icon>error_outline</mat-icon>
                Erreurs de validation
              </div>
              <ul class="validation-errors-list">
                <li *ngIf="assignmentForm.get('idTag')?.invalid">
                  <mat-icon>error</mat-icon>
                  Veuillez sélectionner un tag
                </li>
                <li *ngIf="assignmentForm.get('targetType')?.invalid">
                  <mat-icon>error</mat-icon>
                  Veuillez sélectionner un type de cible
                </li>
                <li *ngIf="assignmentForm.get('targetId')?.invalid">
                  <mat-icon>error</mat-icon>
                  Veuillez sélectionner une cible
                </li>
              </ul>
            </div>

            <form [formGroup]="assignmentForm" (ngSubmit)="onSubmitAssignment()" class="assignment-form">
              <div class="form-grid">
                <!-- Tag Selection -->
                <div class="form-group">
                  <label for="idTag">Tag <span class="required">*</span></label>
                  <select id="idTag" formControlName="idTag" required>
                    <option value="">Sélectionnez un tag</option>
                    <option *ngFor="let tag of tags" [value]="tag.Id">
                      {{ tag.Nom }}
                    </option>
                  </select>
                  <div class="error-message" 
                       *ngIf="showValidationErrors && assignmentForm.get('idTag')?.invalid">
                    Veuillez sélectionner un tag
                  </div>
                </div>

                <!-- Target Type Selection -->
                <div class="form-group">
                  <label for="targetType">Type de Cible <span class="required">*</span></label>
                  <select id="targetType" formControlName="targetType" (change)="onTargetTypeChange()" required>
                    <option value="">Sélectionnez un type</option>
                    <option *ngFor="let type of targetTypes" [value]="type.value">
                      {{ type.label }}
                    </option>
                  </select>
                  <div class="error-message" 
                       *ngIf="showValidationErrors && assignmentForm.get('targetType')?.invalid">
                    Veuillez sélectionner un type de cible
                  </div>
                </div>

                <!-- Target Selection -->
                <div class="form-group">
                  <label for="targetId">Cible <span class="required">*</span></label>
                  <select id="targetId" formControlName="targetId" required [disabled]="!filteredTargets.length">
                    <option value="">Sélectionnez une cible</option>
                    <option *ngFor="let target of filteredTargets" [value]="target.Id">
                      {{ target.Name }}
                    </option>
                  </select>
                  <div class="error-message" 
                       *ngIf="showValidationErrors && assignmentForm.get('targetId')?.invalid">
                    Veuillez sélectionner une cible
                  </div>
                </div>
              </div>

              <div class="form-actions">
                <button type="button" class="btn-cancel" (click)="resetAssignmentForm()">
                  Annuler
                </button>
                <button type="submit" class="btn-submit" [disabled]="isLoading">
                  <mat-icon>assignment_add</mat-icon>
                  Affecter
                </button>
              </div>
            </form>
          </div>
        </div>
      </mat-tab>

      <!-- Tab 2: View Assignments -->
      <mat-tab label="Liste des Affectations">
        <div class="tab-content">
          <div class="table-card">
            <div class="table-header">
              <h2>
                <mat-icon>assignment</mat-icon>
                Affectations de Tags
              </h2>
              <div class="table-stats">
                <span class="stats-badge">{{ assignments.length }} affectation(s)</span>
              </div>
            </div>

            <div class="table-container" *ngIf="assignments.length > 0; else noAssignments">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>Tag</th>
                    <th>Type de Cible</th>
                    <th>Cible</th>
                    <th>Date de Création</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let assignment of assignments">
                    <td>
                      <div class="tag-cell">
                        <mat-icon class="tag-icon">local_offer</mat-icon>
                        {{ getTagName(assignment) }}
                      </div>
                    </td>
                   <td>
  <span class="target-type-badge" [class]="'type-' + getTargetTypeClass(assignment.TargetType)">
    {{ assignment.TargetType }}
  </span>
</td>
                    <td>{{ getTargetName(assignment) }}</td>
                    <td>{{ assignment.CreatedAt | date:'dd/MM/yyyy HH:mm' }}</td>
                    <td>
                      <div class="action-buttons">
                        <button class="btn-action btn-delete" (click)="deleteAssignment(assignment)" title="Supprimer">
                          <mat-icon>delete</mat-icon>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <ng-template #noAssignments>
              <div class="empty-state">
                <mat-icon class="empty-icon">assignment</mat-icon>
                <h3>Aucune affectation trouvée</h3>
                <p>Créez votre première affectation pour commencer</p>
                <button class="btn-primary" (click)="selectedTab = 0">
                  <mat-icon>assignment_add</mat-icon>
                  Créer une Affectation
                </button>
              </div>
            </ng-template>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading">
    <div class="loading-spinner">
      <mat-icon class="spinning">refresh</mat-icon>
      <p>Chargement...</p>
    </div>
  </div>
</div>
