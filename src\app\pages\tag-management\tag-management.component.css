.tag-management-container {
  width: 95%;
  margin: 30px auto;
  padding: 25px;
  background: linear-gradient(145deg, #ffffff, #f9f9f9);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  animation: slideIn 0.5s ease-out;
}

.page-title {
  margin: 0;
}

.title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.subtitle {
  color: #64748b;
  font-size: 16px;
  margin-top: 4px;
}

.title-icon {
  font-size: 30px;
  color: var(--primary);
  background: linear-gradient(45deg, var(--primary), #81C784);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.actions {
  display: flex;
  gap: 15px;
}

.create-button {
  background: linear-gradient(45deg, var(--primary), #81C784);
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.create-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(76, 175, 80, 0.5);
  background: linear-gradient(45deg, #81C784, var(--primary));
}

.search-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  align-items: center;
}

.search-bar input {
  flex: 1;
  max-width: 450px;
  padding: 12px 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #f8fafc;
  color: #4a5568;
}

.search-bar input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.search-bar input::placeholder {
  color: #a0aec0;
}

.search-button {
  background: linear-gradient(45deg, var(--primary), #81C784);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.search-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
  background: linear-gradient(45deg, #81C784, var(--primary));
}

.search-button mat-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
}

.table-section {
  margin-top: 20px;
}

.tag-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tag-icon {
  color: var(--primary);
}

/* Popup styles */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.popup-form {
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideIn 0.3s ease-out;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.popup-header h3 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  color: #2E7D32;
  font-size: 1.25rem;
  font-weight: 600;
}

.popup-header h3 mat-icon {
  color: var(--primary);
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.close-btn:hover {
  background: #f0f0f0;
}

.site-form {
  padding: 24px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4a5568;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  color: #2d3748;
  transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
  outline: none;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
  margin-top: 24px;
}

.btn-cancel,
.btn-submit {
  padding: 10px 24px;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-cancel {
  background: white;
  border: 1px solid #e2e8f0;
  color: #4a5568;
}

.btn-submit {
  background: linear-gradient(45deg, var(--primary), #81C784);
  border: none;
  color: white;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2);
}

.btn-cancel:hover {
  background: #f8fafc;
  border-color: #cbd5e0;
}

.btn-submit:hover {
  background: linear-gradient(45deg, #43A047, #66BB6A);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(76, 175, 80, 0.3);
}

.btn-submit:disabled {
  background: #e2e8f0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.validation-errors {
  background: #fef2f2;
  border-left: 4px solid #ef4444;
  padding: 16px;
  margin-bottom: 24px;
  border-radius: 8px;
}

.validation-errors-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ef4444;
  font-weight: 500;
  margin-bottom: 8px;
}

.validation-errors-list {
  margin: 0;
  padding-left: 24px;
}

.validation-errors-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ef4444;
  font-size: 0.875rem;
  margin-bottom: 4px;
}

.required {
  color: #ef4444;
  margin-left: 4px;
}

.error-message {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 40px;
  font-size: 18px;
  color: var(--primary);
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .actions {
    width: 100%;
  }

  .create-button {
    width: 100%;
    justify-content: center;
  }

  .search-bar {
    flex-direction: column;
  }

  .search-bar input {
    width: 100%;
    max-width: none;
  }

  .search-button {
    width: 100%;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 24px;
  }

  .title-icon {
    font-size: 26px;
  }

  .popup-form {
    width: 95%;
    padding: 15px;
  }

  .popup-header {
    padding: 15px;
  }
}